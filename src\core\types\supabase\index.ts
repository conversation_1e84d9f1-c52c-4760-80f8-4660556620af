/**
 * Supabase database type definitions
 * This file contains all the type definitions for the Supabase database
 */

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          name: string
          phone_number: string | null
          profile_image_url: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          name: string
          phone_number?: string | null
          profile_image_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          name?: string
          phone_number?: string | null
          profile_image_url?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      entities: {
        Row: {
          id: string
          entity_type: string
          name: string
          description: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          entity_type: string
          name: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          entity_type?: string
          name?: string
          description?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      entity_users: {
        Row: {
          id: string
          user_id: string
          entity_id: string
          role: string
          is_primary: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          entity_id: string
          role?: string
          is_primary?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          entity_id?: string
          role?: string
          is_primary?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "entity_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "users"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "entity_users_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }
      artist_profiles: {
        Row: {
          entity_id: string
          genre: string[] | null
          region: string | null
          social_links: Json | null
          pricing_info: Json | null
          profile_image_url: string | null
          banner_image_url: string | null
          public_contact_info: Json | null
          updated_at: string
        }
        Insert: {
          entity_id: string
          genre?: string[] | null
          region?: string | null
          social_links?: Json | null
          pricing_info?: Json | null
          profile_image_url?: string | null
          banner_image_url?: string | null
          public_contact_info?: Json | null
          updated_at?: string
        }
        Update: {
          entity_id?: string
          genre?: string[] | null
          region?: string | null
          social_links?: Json | null
          pricing_info?: Json | null
          profile_image_url?: string | null
          banner_image_url?: string | null
          public_contact_info?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "artist_profiles_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: true
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }
      venue_profiles: {
        Row: {
          entity_id: string
          venue_type: string | null
          address: string | null
          logo_url: string | null
          banner_url: string | null
          invoice_address: string | null
          company_name: string | null
          vat_number: string | null
          public_contact_info: Json | null
          updated_at: string
        }
        Insert: {
          entity_id: string
          venue_type?: string | null
          address?: string | null
          logo_url?: string | null
          banner_url?: string | null
          invoice_address?: string | null
          company_name?: string | null
          vat_number?: string | null
          public_contact_info?: Json | null
          updated_at?: string
        }
        Update: {
          entity_id?: string
          venue_type?: string | null
          address?: string | null
          logo_url?: string | null
          banner_url?: string | null
          invoice_address?: string | null
          company_name?: string | null
          vat_number?: string | null
          public_contact_info?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "venue_profiles_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: true
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }
      artist_details: {
        Row: {
          artist_name: string | null
          banner_image_url: string | null
          description: string | null
          genre: string[] | null
          id: string
          instagram_url: string | null
          mixcloud_url: string | null
          pricing_info: Json | null
          profile_image_url: string | null
          reference_links: string[] | null
          region: string | null
          soundcloud_url: string | null
          spotify_url: string | null
          tiktok_url: string | null
          updated_at: string
          public_email: string | null
          public_phone: string | null
          public_name: string | null
        }
        Insert: {
          artist_name?: string | null
          banner_image_url?: string | null
          description?: string | null
          genre?: string[] | null
          id: string
          instagram_url?: string | null
          mixcloud_url?: string | null
          pricing_info?: Json | null
          profile_image_url?: string | null
          reference_links?: string[] | null
          region?: string | null
          soundcloud_url?: string | null
          spotify_url?: string | null
          tiktok_url?: string | null
          updated_at?: string
          public_email?: string | null
          public_phone?: string | null
          public_name?: string | null
        }
        Update: {
          artist_name?: string | null
          banner_image_url?: string | null
          description?: string | null
          genre?: string[] | null
          id?: string
          instagram_url?: string | null
          mixcloud_url?: string | null
          pricing_info?: string | null
          profile_image_url?: string | null
          reference_links?: string[] | null
          region?: string | null
          soundcloud_url?: string | null
          spotify_url?: string | null
          tiktok_url?: string | null
          updated_at?: string
          public_email?: string | null
          public_phone?: string | null
          public_name?: string | null
        }
        Relationships: []
      }

      agency_profiles: {
        Row: {
          entity_id: string
          profile_image_url: string | null
          banner_image_url: string | null
          public_contact_info: Json | null
          company_name: string | null
          vat_number: string | null
          invoice_address: string | null
          social_links: Json | null
          updated_at: string
        }
        Insert: {
          entity_id: string
          profile_image_url?: string | null
          banner_image_url?: string | null
          public_contact_info?: Json | null
          company_name?: string | null
          vat_number?: string | null
          invoice_address?: string | null
          social_links?: Json | null
          updated_at?: string
        }
        Update: {
          entity_id?: string
          profile_image_url?: string | null
          banner_image_url?: string | null
          public_contact_info?: Json | null
          company_name?: string | null
          vat_number?: string | null
          invoice_address?: string | null
          social_links?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agency_profiles_entity_id_fkey"
            columns: ["entity_id"]
            isOneToOne: true
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }

      agency_artists: {
        Row: {
          id: string
          agency_entity_id: string
          artist_entity_id: string
          relationship_type: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          agency_entity_id: string
          artist_entity_id: string
          relationship_type?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          agency_entity_id?: string
          artist_entity_id?: string
          relationship_type?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agency_artists_agency_entity_id_fkey"
            columns: ["agency_entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agency_artists_artist_entity_id_fkey"
            columns: ["artist_entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }

      agency_clients: {
        Row: {
          id: string
          agency_entity_id: string
          name: string
          type: 'company' | 'person'
          email: string | null
          phone: string | null
          address: string | null
          vat_number: string | null
          notes: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          agency_entity_id: string
          name: string
          type: 'company' | 'person'
          email?: string | null
          phone?: string | null
          address?: string | null
          vat_number?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          agency_entity_id?: string
          name?: string
          type?: 'company' | 'person'
          email?: string | null
          phone?: string | null
          address?: string | null
          vat_number?: string | null
          notes?: string | null
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agency_clients_agency_entity_id_fkey"
            columns: ["agency_entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }

      agency_client_contacts: {
        Row: {
          id: string
          client_id: string
          name: string
          email: string | null
          phone: string | null
          position: string | null
          is_primary: boolean
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          client_id: string
          name: string
          email?: string | null
          phone?: string | null
          position?: string | null
          is_primary?: boolean
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          client_id?: string
          name?: string
          email?: string | null
          phone?: string | null
          position?: string | null
          is_primary?: boolean
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "agency_client_contacts_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "agency_clients"
            referencedColumns: ["id"]
          }
        ]
      }

      bookings: {
        Row: {
          artist_id: string
          booking_end: string
          booking_start: string
          created_at: string | null
          created_by: string
          description: string | null
          id: string
          location: string | null
          notes: string | null
          price: number
          pricing_type: string
          status: string
          title: string
          updated_at: string | null
          venue_id: string
        }
        Insert: {
          artist_id: string
          booking_end: string
          booking_start: string
          created_at?: string | null
          created_by: string
          description?: string | null
          id?: string
          location?: string | null
          notes?: string | null
          price: number
          pricing_type: string
          status?: string
          title: string
          updated_at?: string | null
          venue_id: string
        }
        Update: {
          artist_id?: string
          booking_end?: string
          booking_start?: string
          created_at?: string | null
          created_by?: string
          description?: string | null
          id?: string
          location?: string | null
          notes?: string | null
          price?: number
          pricing_type?: string
          status?: string
          title?: string
          updated_at?: string | null
          venue_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "bookings_artist_id_fkey"
            columns: ["artist_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venue_details"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "bookings_venue_id_fkey"
            columns: ["venue_id"]
            isOneToOne: false
            referencedRelation: "venue_with_primary_users"
            referencedColumns: ["venue_id"]
          },
        ]
      }

      contract_templates: {
        Row: {
          content: string
          created_at: string | null
          id: string
          is_public: boolean | null
          owner_id: string
          template_sections: Json | null
          title: string
          updated_at: string | null
          variables: Json | null
          description: string | null
          venue_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          is_public?: boolean | null
          owner_id: string
          template_sections?: Json | null
          title: string
          updated_at?: string | null
          variables?: Json | null
          description?: string | null
          venue_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          is_public?: boolean | null
          owner_id?: string
          template_sections?: Json | null
          title?: string
          updated_at?: string | null
          variables?: Json | null
          description?: string | null
          venue_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "contract_templates_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }

      document_templates: {
        Row: {
          content: string
          created_at: string | null
          id: string
          is_public: boolean | null
          owner_id: string
          template_sections: Json | null
          title: string
          updated_at: string | null
          variables: Json | null
          description: string | null
          document_type: string
          venue_id: string | null
        }
        Insert: {
          content: string
          created_at?: string | null
          id?: string
          is_public?: boolean | null
          owner_id: string
          template_sections?: Json | null
          title: string
          updated_at?: string | null
          variables?: Json | null
          description?: string | null
          document_type: string
          venue_id?: string | null
        }
        Update: {
          content?: string
          created_at?: string | null
          id?: string
          is_public?: boolean | null
          owner_id?: string
          template_sections?: Json | null
          title?: string
          updated_at?: string | null
          variables?: Json | null
          description?: string | null
          document_type?: string
          venue_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "document_templates_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }

      documents: {
        Row: {
          artist_signature_date: string | null
          booking_id: string
          content: string | null
          created_at: string | null
          created_by: string
          description: string | null
          document_type: string
          file_type: string | null
          file_url: string | null
          id: string
          is_required: boolean | null
          notes: string | null
          signed_by_artist: boolean | null
          signed_by_venue: boolean | null
          status: string
          template_id: string | null
          title: string
          updated_at: string | null
          venue_signature_date: string | null
          share_id: string | null
          is_public: boolean | null
        }
        Insert: {
          artist_signature_date?: string | null
          booking_id: string
          content?: string | null
          created_at?: string | null
          created_by: string
          description?: string | null
          document_type: string
          file_type?: string | null
          file_url?: string | null
          id?: string
          is_required?: boolean | null
          notes?: string | null
          signed_by_artist?: boolean | null
          signed_by_venue?: boolean | null
          status?: string
          template_id?: string | null
          title: string
          updated_at?: string | null
          venue_signature_date?: string | null
          share_id?: string | null
          is_public?: boolean | null
        }
        Update: {
          artist_signature_date?: string | null
          booking_id?: string
          content?: string | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          document_type?: string
          file_type?: string | null
          file_url?: string | null
          id?: string
          is_required?: boolean | null
          notes?: string | null
          signed_by_artist?: boolean | null
          signed_by_venue?: boolean | null
          status?: string
          template_id?: string | null
          title?: string
          updated_at?: string | null
          venue_signature_date?: string | null
          share_id?: string | null
          is_public?: boolean | null
        }
        Relationships: [
          {
            foreignKeyName: "documents_booking_id_fkey"
            columns: ["booking_id"]
            isOneToOne: false
            referencedRelation: "bookings"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "documents_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      agency_with_artists: {
        Row: {
          agency_id: string
          agency_name: string
          agency_profile_image: string | null
          agency_banner_image: string | null
          artist_entity_id: string | null
          artist_name: string | null
          relationship_type: string | null
          artist_profile_image: string | null
          artist_genre: string[] | null
          artist_region: string | null
        }
        Relationships: [
          {
            foreignKeyName: "agency_profiles_entity_id_fkey"
            columns: ["agency_id"]
            isOneToOne: true
            referencedRelation: "entities"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "agency_artists_artist_entity_id_fkey"
            columns: ["artist_entity_id"]
            isOneToOne: false
            referencedRelation: "entities"
            referencedColumns: ["id"]
          }
        ]
      }
    }
    Functions: {}
    Enums: {
      user_type: "artist" | "venue" | "agency"
    }
  }
}
