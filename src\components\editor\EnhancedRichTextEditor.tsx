import React, { useEffect, useState } from 'react';
import { useE<PERSON><PERSON>, EditorContent, BubbleMenu, FloatingMenu } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import Typography from '@tiptap/extension-typography';
import Underline from '@tiptap/extension-underline';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
// Removed FontFamily import
import Highlight from '@tiptap/extension-highlight';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableCell from '@tiptap/extension-table-cell';
import TableHeader from '@tiptap/extension-table-header';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import VariableExtension from './VariableExtension';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  List,
  ListOrdered,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  Heading1,
  Heading2,
  Heading3,
  Type,
  Code,
  Quote,
  Undo,
  Redo,
  Variable as VariableIcon,
  Table as TableIcon,
  Image as ImageIcon,
  Link as LinkIcon,
  Unlink,
  Indent,
  Outdent,
  Trash2,
  Palette,
  Highlighter,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  TextCursorInput,
  SquareEqual,
  RowsIcon,
  ColumnsIcon,
  Trash,
  Code2
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import { cn } from '@/core';
import { toast } from 'sonner';
import { Variable } from '@/core/utils/contract-utils';
import './EditorStyles.css';

interface RichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  variables?: Variable[];
  onInsertVariable?: (variable: Variable) => void;
  className?: string;
  editorClassName?: string;
  readOnly?: boolean;
}

const EnhancedRichTextEditor = ({
  content,
  onChange,
  placeholder = 'Start typing...',
  variables = [],
  onInsertVariable,
  className,
  editorClassName,
  readOnly = false
}: RichTextEditorProps) => {
  const [isMounted, setIsMounted] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [linkDialogOpen, setLinkDialogOpen] = useState(false);
  const [showHtmlEditor, setShowHtmlEditor] = useState(false);
  const [htmlContent, setHtmlContent] = useState('');

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        paragraph: {
          HTMLAttributes: {
            class: 'whitespace-pre-wrap',
          },
        },
        hardBreak: {
          keepMarks: true,
        },
      }),
      Placeholder.configure({
        placeholder,
      }),
      Typography,
      Underline,
      TextStyle,
      Color,
      Highlight.configure({ multicolor: true }),
      Subscript,
      Superscript,
      Table.configure({
        resizable: true,
        allowTableNodeSelection: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Image,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-500 underline',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph', 'blockquote'],
      }),
      VariableExtension.configure({
        HTMLAttributes: {
          class: 'variable',
        },
      }),
    ],
    content,
    editable: !readOnly,
    onUpdate: ({ editor }) => {
      const html = editor.getHTML();
      onChange(html);
      setHtmlContent(html);
    },
  });

  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (editor && isMounted) {
      try {
        const currentEditorContent = editor.getHTML();

        // Log the content lengths for debugging
        (`Content prop length: ${content.length}, Editor content length: ${currentEditorContent.length}`);

        // Always update the editor content when the prop changes
        // This ensures the editor always reflects the latest content from the parent
        if (content !== currentEditorContent) {
          ('Content prop changed, updating editor');

          // Store the content in both states to ensure consistency
          editor.commands.setContent(content);
          setHtmlContent(content);

          // Save to localStorage as a backup
          try {
            localStorage.setItem('editor_content_backup', content);
          } catch (e) {
            console.error('Failed to save backup to localStorage:', e);
          }
        }
      } catch (error) {
        console.error('Error updating editor content:', error);
        toast.error('There was an error updating the editor content. Please try again.');
      }
    }
  }, [editor, isMounted, content]);

  // Update editor content when HTML is edited directly or when switching modes
  useEffect(() => {
    if (editor) {
      try {
        // When in HTML mode, we don't need to update the editor immediately
        // as it will be updated when switching back to visual mode
        // But we do need to ensure the editor has the latest content when not in HTML mode
        if (!showHtmlEditor && htmlContent !== editor.getHTML()) {
          ('HTML content changed, updating editor');
          editor.commands.setContent(htmlContent);
        }
      } catch (error) {
        console.error('Error syncing HTML content with editor:', error);
      }
    }
  }, [showHtmlEditor, htmlContent, editor]);

  if (!isMounted) {
    return null;
  }

  const variablesByCategory = variables.reduce((acc, variable) => {
    if (!acc[variable.category]) {
      acc[variable.category] = [];
    }
    acc[variable.category].push(variable);
    return acc;
  }, {} as Record<string, Variable[]>);

  const categoryLabels = {
    booking: 'Booking',
    artist: 'Artist',
    venue: 'Venue',
    other: 'Other'
  };

  const handleSetLink = () => {
    if (editor && linkUrl) {
      editor.chain().focus().setLink({ href: linkUrl }).run();
      setLinkUrl('');
      setLinkDialogOpen(false);
    }
  };

  const handleInsertImage = () => {
    const url = prompt('Enter image URL');
    if (url && editor) {
      editor.chain().focus().setImage({ src: url }).run();
    }
  };

  const handleInsertTable = () => {
    if (editor) {
      editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
    }
  };

  const handleAddTableRow = () => {
    if (editor) {
      editor.chain().focus().addRowAfter().run();
    }
  };

  const handleAddTableColumn = () => {
    if (editor) {
      editor.chain().focus().addColumnAfter().run();
    }
  };

  const handleDeleteTableRow = () => {
    if (editor) {
      editor.chain().focus().deleteRow().run();
    }
  };

  const handleDeleteTableColumn = () => {
    if (editor) {
      editor.chain().focus().deleteColumn().run();
    }
  };

  const handleDeleteTable = () => {
    if (editor) {
      editor.chain().focus().deleteTable().run();
    }
  };

  const handleMergeCells = () => {
    if (editor) {
      editor.chain().focus().mergeCells().run();
    }
  };

  const handleSplitCell = () => {
    if (editor) {
      editor.chain().focus().splitCell().run();
    }
  };

  const handleToggleHtmlEditor = () => {
    // Before toggling, ensure content is synchronized
    if (showHtmlEditor) {
      // Switching from HTML to Visual - update the editor content
      if (editor) {
        try {
          // Save current cursor position
          const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
          const cursorPos = textarea?.selectionStart || 0;

          // Update editor content
          editor.commands.setContent(htmlContent);

          // Notify parent about the change
          onChange(htmlContent);

          ('Switched to Visual Editor with content:', htmlContent.substring(0, 100) + '...');
        } catch (error) {
          console.error('Error switching to visual editor:', error);
          toast.error('There was an error switching to the visual editor. Your content has been preserved in HTML mode.');
          return; // Don't switch if there's an error
        }
      }
    } else {
      // Switching from Visual to HTML - update the HTML content
      if (editor) {
        try {
          const html = editor.getHTML();
          setHtmlContent(html);
          ('Switched to HTML Editor with content:', html.substring(0, 100) + '...');
        } catch (error) {
          console.error('Error switching to HTML editor:', error);
          toast.error('There was an error switching to the HTML editor. Your content has been preserved in visual mode.');
          return; // Don't switch if there's an error
        }
      }
    }

    // Toggle the editor mode
    setShowHtmlEditor(!showHtmlEditor);
  };

  const handleHtmlContentChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!isMounted) return;

    const newContent = e.target.value;
    setHtmlContent(newContent);

    // Immediately notify parent component of the change
    // This ensures the parent always has the latest content
    onChange(newContent);

    // Also save to localStorage as a backup
    try {
      localStorage.setItem('editor_content_backup', newContent);
    } catch (e) {
      console.error('Failed to save backup to localStorage:', e);
    }
  };

  const handleInsertVariable = (variable: Variable) => {
    if (editor && onInsertVariable) {
      // Insert the variable based on the current mode
      if (showHtmlEditor) {
        // If in HTML mode, insert the HTML code at cursor position
        const variableHtml = `<span class="variable" data-variable-id="${variable.id}" data-variable-name="${variable.name}">${variable.label}</span>`;

        const textarea = document.querySelector('textarea') as HTMLTextAreaElement;
        if (textarea) {
          const cursorPos = textarea.selectionStart;
          const textBefore = htmlContent.substring(0, cursorPos);
          const textAfter = htmlContent.substring(cursorPos);
          const newHtml = textBefore + variableHtml + textAfter;
          setHtmlContent(newHtml);

          // Set cursor position after the inserted variable and show a toast notification
          setTimeout(() => {
            textarea.focus();
            textarea.setSelectionRange(cursorPos + variableHtml.length, cursorPos + variableHtml.length);
            toast.success(`Variable '${variable.label}' inserted`);
          }, 0);
        }
      } else {
        // In visual editor mode, use our custom variable extension
        editor.chain().focus().insertVariable({
          id: variable.id,
          name: variable.name,
          label: variable.label
        }).run();

        toast.success(`Variable '${variable.label}' inserted`);
      }

      // Notify parent component about the variable insertion
      onInsertVariable(variable);
    }
  };

  return (
    <div className={cn("rich-text-editor border rounded-md flex flex-col", className)}>
      {!readOnly && editor && (
        <div className="flex flex-wrap gap-1 p-2 border-b bg-muted/20">
          {/* Toggle HTML Editor */}
          <Button
            variant="outline"
            size="sm"
            onClick={handleToggleHtmlEditor}
            className={cn(
              "flex items-center gap-1",
              showHtmlEditor ? 'bg-amber-100 hover:bg-amber-200 border-amber-300' : 'bg-slate-50 hover:bg-slate-100'
            )}
            type="button"
            title="Toggle HTML Source Editor"
          >
            <Code2 className="h-4 w-4" />
            <span>{showHtmlEditor ? 'Visual Editor' : 'HTML Source'}</span>
          </Button>

          <div className="w-px h-6 bg-border mx-1" />
          {/* Text formatting */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleBold().run()}
            className={editor.isActive('bold') ? 'bg-muted' : ''}
            type="button"
            title="Bold"
          >
            <Bold className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleItalic().run()}
            className={editor.isActive('italic') ? 'bg-muted' : ''}
            type="button"
            title="Italic"
          >
            <Italic className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            className={editor.isActive('underline') ? 'bg-muted' : ''}
            type="button"
            title="Underline"
          >
            <UnderlineIcon className="h-4 w-4" />
          </Button>

          <div className="w-px h-6 bg-border mx-1" />

          {/* Headings */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center gap-1 px-2 min-w-[100px] justify-start"
                type="button"
                title="Text Style"
              >
                <Type className="h-4 w-4" />
                <span className="text-sm">
                  {editor.isActive('heading', { level: 1 }) && 'Heading 1'}
                  {editor.isActive('heading', { level: 2 }) && 'Heading 2'}
                  {editor.isActive('heading', { level: 3 }) && 'Heading 3'}
                  {editor.isActive('paragraph') && 'Normal text'}
                  {!editor.isActive('heading') && !editor.isActive('paragraph') && 'Text Style'}
                </span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().setParagraph().run()}
                className={editor.isActive('paragraph') ? 'bg-muted' : ''}
              >
                <span className="font-normal">Normal text</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
                className={editor.isActive('heading', { level: 1 }) ? 'bg-muted' : ''}
              >
                <span className="text-2xl font-bold">Heading 1</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
                className={editor.isActive('heading', { level: 2 }) ? 'bg-muted' : ''}
              >
                <span className="text-xl font-semibold">Heading 2</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => editor.chain().focus().toggleHeading({ level: 3 }).run()}
                className={editor.isActive('heading', { level: 3 }) ? 'bg-muted' : ''}
              >
                <span className="text-lg font-medium">Heading 3</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Text alignment */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            className={editor.isActive({ textAlign: 'left' }) ? 'bg-muted' : ''}
            type="button"
            title="Align Left"
          >
            <AlignLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            className={editor.isActive({ textAlign: 'center' }) ? 'bg-muted' : ''}
            type="button"
            title="Align Center"
          >
            <AlignCenter className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            className={editor.isActive({ textAlign: 'right' }) ? 'bg-muted' : ''}
            type="button"
            title="Align Right"
          >
            <AlignRight className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            className={editor.isActive({ textAlign: 'justify' }) ? 'bg-muted' : ''}
            type="button"
            title="Justify"
          >
            <AlignJustify className="h-4 w-4" />
          </Button>

          {/* Text style */}
          <div className="w-px h-6 bg-border mx-1" />

          {/* Text Color */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" type="button" title="Text Color">
                <Palette className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64" align="start">
              <div className="space-y-2">
                <h4 className="font-medium">Text Color</h4>
                <div className="grid grid-cols-5 gap-1">
                  {['#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF', '#808080', '#800000', '#808000', '#008000', '#800080', '#008080', '#000080', '#555555'].map((color) => (
                    <Button
                      key={color}
                      variant="outline"
                      className="w-8 h-8 p-0"
                      style={{ backgroundColor: color }}
                      onClick={() => editor.chain().focus().setColor(color).run()}
                    />
                  ))}
                </div>
                <Button
                  variant="outline"
                  className="w-full mt-2"
                  onClick={() => editor.chain().focus().unsetColor().run()}
                >
                  Reset color
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* Highlight */}
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" type="button" title="Highlight">
                <Highlighter className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-64" align="start">
              <div className="space-y-2">
                <h4 className="font-medium">Highlight Color</h4>
                <div className="grid grid-cols-5 gap-1">
                  {['#FFFF00', '#00FFFF', '#FF00FF', '#FF0000', '#00FF00', '#0000FF', '#FFFFFF', '#F0F0F0', '#D3D3D3', '#FFC0CB', '#FFD700', '#FFA500', '#90EE90', '#87CEFA'].map((color) => (
                    <Button
                      key={color}
                      variant="outline"
                      className="w-8 h-8 p-0"
                      style={{ backgroundColor: color }}
                      onClick={() => editor.chain().focus().toggleHighlight({ color }).run()}
                    />
                  ))}
                </div>
                <Button
                  variant="outline"
                  className="w-full mt-2"
                  onClick={() => editor.chain().focus().unsetHighlight().run()}
                >
                  Remove highlight
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* Subscript/Superscript */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleSubscript().run()}
            className={editor.isActive('subscript') ? 'bg-muted' : ''}
            type="button"
            title="Subscript"
          >
            <SubscriptIcon className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleSuperscript().run()}
            className={editor.isActive('superscript') ? 'bg-muted' : ''}
            type="button"
            title="Superscript"
          >
            <SuperscriptIcon className="h-4 w-4" />
          </Button>

          <div className="w-px h-6 bg-border mx-1" />

          {/* Lists */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            className={editor.isActive('bulletList') ? 'bg-muted' : ''}
            type="button"
            title="Bullet List"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            className={editor.isActive('orderedList') ? 'bg-muted' : ''}
            type="button"
            title="Numbered List"
          >
            <ListOrdered className="h-4 w-4" />
          </Button>

          {/* Indentation */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              if (editor.isActive('bulletList') || editor.isActive('orderedList')) {
                editor.chain().focus().sinkListItem('listItem').run();
              } else {
                // For non-list items, we'll add a custom indent class
                editor.chain().focus().setTextAlign('left').run();
                editor.chain().focus().setParagraph().run();
                editor.chain().focus().setAttributes('paragraph', { class: 'pl-8' }).run();
              }
            }}
            type="button"
            title="Indent"
          >
            <Indent className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => {
              if (editor.isActive('bulletList') || editor.isActive('orderedList')) {
                editor.chain().focus().liftListItem('listItem').run();
              } else {
                // For non-list items, we'll remove the custom indent class
                editor.chain().focus().setTextAlign('left').run();
                editor.chain().focus().setParagraph().run();
                editor.chain().focus().removeAttributes('paragraph', 'class').run();
              }
            }}
            type="button"
            title="Outdent"
          >
            <Outdent className="h-4 w-4" />
          </Button>

          <div className="w-px h-6 bg-border mx-1" />

          {/* Block elements */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            className={editor.isActive('blockquote') ? 'bg-muted' : ''}
            type="button"
            title="Quote"
          >
            <Quote className="h-4 w-4" />
          </Button>

          {/* Table */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" type="button" title="Table Options">
                <TableIcon className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent>
              <DropdownMenuItem onClick={handleInsertTable}>
                <TableIcon className="h-4 w-4 mr-2" /> Insert Table
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleAddTableRow} disabled={!editor?.isActive('table')}>
                <RowsIcon className="h-4 w-4 mr-2" /> Add Row
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleAddTableColumn} disabled={!editor?.isActive('table')}>
                <ColumnsIcon className="h-4 w-4 mr-2" /> Add Column
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDeleteTableRow} disabled={!editor?.isActive('table')}>
                <Trash className="h-4 w-4 mr-2" /> Delete Row
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleDeleteTableColumn} disabled={!editor?.isActive('table')}>
                <Trash className="h-4 w-4 mr-2" /> Delete Column
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleMergeCells} disabled={!editor?.isActive('table')}>
                <SquareEqual className="h-4 w-4 mr-2" /> Merge Cells
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSplitCell} disabled={!editor?.isActive('table')}>
                <SquareEqual className="h-4 w-4 mr-2" /> Split Cell
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleDeleteTable} disabled={!editor?.isActive('table')} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" /> Delete Table
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Link */}
          <Popover open={linkDialogOpen} onOpenChange={setLinkDialogOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                className={editor.isActive('link') ? 'bg-muted' : ''}
                type="button"
                title="Insert Link"
              >
                <LinkIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80" align="start">
              <div className="space-y-2">
                <h4 className="font-medium">Insert Link</h4>
                <div className="space-y-2">
                  <Label htmlFor="url">URL</Label>
                  <Input
                    id="url"
                    value={linkUrl}
                    onChange={(e) => setLinkUrl(e.target.value)}
                    placeholder="https://example.com"
                  />
                </div>
                <div className="flex justify-end space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setLinkDialogOpen(false)}
                  >
                    Cancel
                  </Button>
                  <Button
                    size="sm"
                    onClick={handleSetLink}
                    disabled={!linkUrl}
                  >
                    Save
                  </Button>
                </div>
              </div>
            </PopoverContent>
          </Popover>

          {/* Unlink */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().unsetLink().run()}
            disabled={!editor.isActive('link')}
            type="button"
            title="Remove Link"
          >
            <Unlink className="h-4 w-4" />
          </Button>

          {/* Image */}
          <Button
            variant="ghost"
            size="icon"
            onClick={handleInsertImage}
            type="button"
            title="Insert Image"
          >
            <ImageIcon className="h-4 w-4" />
          </Button>

          <div className="w-px h-6 bg-border mx-1" />

          {/* Undo/Redo */}
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            type="button"
            title="Undo"
          >
            <Undo className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            type="button"
            title="Redo"
          >
            <Redo className="h-4 w-4" />
          </Button>

          {variables.length > 0 && (
            <>
              <div className="w-px h-6 bg-border mx-1" />

              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="flex items-center gap-1 bg-blue-50 hover:bg-blue-100" type="button" title="Insert Variable">
                    <VariableIcon className="h-4 w-4" />
                    <span>Insert Variable</span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-96" align="start">
                  <div className="space-y-3">
                    <div>
                      <h4 className="font-medium text-lg">Insert Variable</h4>
                      <p className="text-sm text-muted-foreground">
                        Variables will be replaced with actual data when the contract is generated.
                      </p>
                    </div>
                    <ScrollArea className="h-72 border rounded p-2">
                      <div className="space-y-4 pr-3">
                        {Object.entries(variablesByCategory).map(([category, vars]) => (
                          <div key={category} className="space-y-2">
                            <h5 className="text-sm font-medium bg-muted px-2 py-1 rounded">{categoryLabels[category as keyof typeof categoryLabels]}</h5>
                            <div className="grid grid-cols-1 gap-1 pl-2">
                              {vars.map((variable) => (
                                <Button
                                  key={variable.id}
                                  variant="ghost"
                                  size="sm"
                                  className="justify-start text-left font-normal hover:bg-blue-50 hover:text-blue-700"
                                  onClick={() => handleInsertVariable(variable)}
                                  type="button"
                                >
                                  <span className="truncate">{variable.label}</span>
                                  {variable.description && (
                                    <span className="ml-2 text-xs text-muted-foreground hidden group-hover:inline-block">{variable.description}</span>
                                  )}
                                </Button>
                              ))}
                            </div>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </PopoverContent>
              </Popover>
            </>
          )}
        </div>
      )}

      <div className="flex-grow flex flex-col h-[750px] overflow-hidden">
        {showHtmlEditor ? (
          <div className="p-4 space-y-3 flex flex-col h-full">
            <div className="bg-blue-50 p-3 rounded border border-blue-200 text-sm">
              <p className="font-medium text-blue-800">HTML Source Editor</p>
              <p className="text-blue-700 mt-1">You can edit the HTML directly here. Use the "Insert Variable" button to add variables that will be replaced with actual data when the contract is generated.</p>
            </div>
            <div className="flex-grow relative border rounded overflow-hidden">
              <textarea
                value={htmlContent}
                onChange={handleHtmlContentChange}
                className="absolute inset-0 w-full h-full font-mono text-base p-4 focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none"
                placeholder="Edit HTML directly..."
                spellCheck="false"
                wrap="soft"
              />
            </div>
          </div>
        ) : (
          <div className="flex-grow flex flex-col overflow-hidden">
            <EditorContent
              editor={editor}
              className={cn(
                "prose prose-base max-w-none focus:outline-none flex-grow overflow-y-auto",
                "prose-table:border-collapse prose-td:border prose-td:border-gray-300 prose-td:p-2",
                "prose-th:border prose-th:border-gray-300 prose-th:p-2 prose-th:bg-gray-100",
                "prose-p:whitespace-pre-wrap prose-li:whitespace-pre-wrap", // Preserve whitespace in paragraphs and list items
                editorClassName,
                readOnly && "pointer-events-none opacity-90"
              )}
            />
          </div>
        )}
      </div>


    </div>
  );
};

export default EnhancedRichTextEditor;
