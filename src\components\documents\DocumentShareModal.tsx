import { useState, useEffect } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';
import { Copy, Link2, Loader2, Check, Share2 } from 'lucide-react';
import { generateDocumentShareId, toggleDocumentPublicStatus } from '@/features/documents/api/documentHelpers';
import { useAuth } from '@/contexts/AuthContext';

interface DocumentShareModalProps {
  open: boolean;
  onClose: () => void;
  documentId: string;
  documentTitle: string;
  shareId?: string;
  isPublic?: boolean;
}

const DocumentShareModal = ({
  open,
  onClose,
  documentId,
  documentTitle,
  shareId: initialShareId,
  isPublic: initialIsPublic = false
}: DocumentShareModalProps) => {
  const [shareId, setShareId] = useState<string | null>(initialShareId || null);
  const [isPublic, setIsPublic] = useState<boolean>(initialIsPublic);
  const [loading, setLoading] = useState<boolean>(false);
  const [copied, setCopied] = useState<boolean>(false);
  useAuth(); // Keep the auth context for potential future use

  // Generate the full share URL
  const shareUrl = shareId ? `${window.location.origin}/shared/documents/${shareId}` : '';

  useEffect(() => {
    // If the document is already public and has a shareId, use it
    if (initialShareId) {
      setShareId(initialShareId);
      setIsPublic(initialIsPublic);
    }
  }, [initialShareId, initialIsPublic]);

  const handleGenerateShareId = async () => {
    setLoading(true);
    try {
      const result = await generateDocumentShareId(documentId);

      if (result.error) {
        throw result.error;
      }

      setShareId(result.data);
      setIsPublic(true);
      toast.success("Share link generated successfully");
    } catch (error) {
      console.error('Error generating share link:', error);
      toast.error("Failed to generate share link");
    } finally {
      setLoading(false);
    }
  };

  const handleTogglePublicStatus = async () => {
    setLoading(true);
    try {
      const result = await toggleDocumentPublicStatus(documentId);

      if (result.error) {
        throw result.error;
      }

      setIsPublic(result.data);

      if (result.data) {
        toast.success("Document is now public");
      } else {
        toast.success("Document is now private");
      }
    } catch (error) {
      console.error('Error toggling public status:', error);
      toast.error("Failed to update document sharing status");
    } finally {
      setLoading(false);
    }
  };

  const handleCopyLink = () => {
    if (shareUrl) {
      navigator.clipboard.writeText(shareUrl);
      setCopied(true);
      toast.success("Link copied to clipboard");

      // Reset copied state after 2 seconds
      setTimeout(() => {
        setCopied(false);
      }, 2000);
    }
  };



  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Share Document</DialogTitle>
          <DialogDescription>
            Share "{documentTitle}" with others via a public link.
          </DialogDescription>
        </DialogHeader>

        <div className="mt-4 space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="public-status"
              checked={isPublic}
              onCheckedChange={handleTogglePublicStatus}
              disabled={loading}
            />
            <Label htmlFor="public-status">
              Make document publicly accessible
            </Label>
          </div>

          {isPublic ? (
            <>
              <div className="flex items-center space-x-2">
                <Input
                  value={shareUrl}
                  readOnly
                  className="flex-1"
                />
                <Button
                  variant="outline"
                  size="icon"
                  onClick={handleCopyLink}
                  disabled={!shareId}
                >
                  {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                </Button>
              </div>
              <p className="text-sm text-muted-foreground">
                Anyone with this link can view this document without logging in.
              </p>
            </>
          ) : (
            <div className="flex flex-col items-center justify-center py-6">
              <Link2 className="h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-center mb-4">
                Generate a public link to share this document with anyone.
              </p>
              <Button
                onClick={handleGenerateShareId}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <Share2 className="mr-2 h-4 w-4" />
                    Generate Share Link
                  </>
                )}
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default DocumentShareModal;
