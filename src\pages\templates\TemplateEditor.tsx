import React, { useState, useEffect, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import DashboardLayout from '@/components/layout/DashboardLayout';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { AlertTriangle, ArrowLeft, Save, Eye, FileText, Loader2 } from 'lucide-react';
import { supabase } from '@/core/api/supabase';
import { getUserVenues } from '@/features/entities/api/venueHelpers';
import { getUserEntity } from '@/features/entities/api/profileHelpers';
import { useAuth } from '@/contexts/AuthContext';
import { Variable } from '@/components/editor/RichTextEditor';
import EnhancedRichTextEditor from '@/components/editor/EnhancedRichTextEditor';
import { contractVariables, replaceEnhancedVariablesInTemplate as replaceVariablesInTemplate } from '@/core/utils/contract-utils';

interface TemplateFormData {
  title: string;
  description: string;
  content: string;
  variables: Variable[];
  document_type: string;
}

const defaultTemplate = ``;

const TemplateEditor = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [venueId, setVenueId] = useState<string | null>(null);
  const [formData, setFormData] = useState<TemplateFormData>({
    title: '',
    description: '',
    content: defaultTemplate,
    variables: [],
    document_type: 'contract'
  });

  // Track the last known good content to prevent content loss
  const [lastKnownGoodContent, setLastKnownGoodContent] = useState<string>(defaultTemplate);
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('edit');

  // Store the content before switching tabs
  const [contentBeforeTabSwitch, setContentBeforeTabSwitch] = useState<string>('');

  // Handle tab changes safely
  const handleTabChange = (value: string) => {
    // Don't do anything if we're already on this tab
    if (value === activeTab) return;

    // Log the current state before switching
    (`Tab change requested: ${activeTab} -> ${value}. Content length: ${formData.content.length}`);

    // IMPORTANT: Store the current content before switching tabs
    if (activeTab === 'edit' && value === 'preview') {
      // When switching from edit to preview, store the current content
      ('Storing content before switching to preview');
      setContentBeforeTabSwitch(formData.content);

      // Also save to localStorage as a backup
      try {
        localStorage.setItem('template_content_backup', formData.content);
      } catch (e) {
        console.error('Failed to save backup to localStorage:', e);
      }

      // If the content is not empty, also update lastKnownGoodContent
      if (formData.content && formData.content.trim() !== '') {
        setLastKnownGoodContent(formData.content);
      }
    }

    // The actual content restoration will happen in the useEffect that watches for tab changes
    // This prevents duplicate content restoration and makes the code more maintainable

    // Set the active tab
    setActiveTab(value);
  };
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const previewData = {
    booking: {
      title: 'Live Music Performance',
      booking_start: new Date().toISOString(),
      booking_end: new Date(Date.now() + 3 * 60 * 60 * 1000).toISOString(),
      location: 'Main Stage',
      price: 150,
      pricing_type: 'hourly',
      description: 'Live music performance for our Friday night event',
      notes: 'Please arrive early for sound check'
    },
    artist: {
      artist_name: 'DJ Example',
      public_name: 'John Doe',
      public_email: '<EMAIL>',
      public_phone: '+1234567890',
      genre: ['House', 'Techno']
    },
    venue: {
      venue_name: 'The Music Venue',
      address: '123 Music Street, City',
      contact_name: 'Jane Smith',
      email: '<EMAIL>',
      phone: '+0987654321'
    },
    contract: {
      id: 'CONT-12345'
    }
  };

  useEffect(() => {
    if (user) {
      getCurrentVenue();
    }
  }, [user]);

  useEffect(() => {
    if (id && id !== 'new' && venueId) {
      loadTemplate(id);
    }
  }, [id, venueId]);

  // Track tab changes to restore content only when switching tabs
  const [previousTab, setPreviousTab] = useState<string>(activeTab);
  const [hasRestoredContent, setHasRestoredContent] = useState<boolean>(false);

  // This effect ensures the editor content is properly restored ONLY when switching back to edit mode
  useEffect(() => {
    // Only run this effect when the tab actually changes
    if (activeTab !== previousTab) {
      (`Tab changed from ${previousTab} to ${activeTab}`);

      // Only restore content when switching from preview to edit
      if (activeTab === 'edit' && previousTab === 'preview') {
        ('Switching from preview to edit tab');

        // Set a flag to prevent multiple restorations
        if (!hasRestoredContent) {
          // If we have content stored from before the tab switch, use it
          if (contentBeforeTabSwitch && contentBeforeTabSwitch.trim() !== '') {
            ('Restoring content from contentBeforeTabSwitch in useEffect');
            setFormData(prev => ({ ...prev, content: contentBeforeTabSwitch }));
            setHasRestoredContent(true);
          }
          // If content is empty, try to restore from lastKnownGoodContent
          else if (!formData.content || formData.content.trim() === '') {
            if (lastKnownGoodContent && lastKnownGoodContent.trim() !== '') {
              ('Restoring content from lastKnownGoodContent when switching to edit tab');
              setFormData(prev => ({ ...prev, content: lastKnownGoodContent }));
              setHasRestoredContent(true);
            }
          }
        }
      } else if (activeTab === 'preview') {
        // Reset the restoration flag when switching to preview
        setHasRestoredContent(false);
      }

      // Update the previous tab
      setPreviousTab(activeTab);
    }
  }, [activeTab, previousTab, contentBeforeTabSwitch, formData.content, lastKnownGoodContent, hasRestoredContent]);

  const getCurrentVenue = async () => {
    try {
      if (!user) return;

      // Get the user's entity ID directly
      const userEntityData = await getUserEntity(user.id);

      if (!userEntityData || !userEntityData.entity_id) {
        console.error('Error fetching user entity');
        toast.error("Could not find your entity information");
        return;
      }

      // Set the entity ID as the venue ID (for backward compatibility)
      setVenueId(userEntityData.entity_id);

      ('Using entity ID for template:', userEntityData.entity_id);
    } catch (error) {
      console.error('Error getting user entity:', error);
      toast.error('Failed to get entity information');
    }
  };

  const loadTemplate = async (templateId: string) => {
    setLoading(true);
    try {
      const { data, error } = await supabase
        .from('document_templates')
        .select('*')
        .eq('id', templateId)
        .single();

      if (error) throw error;

      if (data) {
        const content = data.content || defaultTemplate;

        // Set the form data
        setFormData({
          title: data.title,
          description: data.description || '',
          content: content,
          variables: data.variables ? JSON.parse(data.variables as string) as Variable[] : [],
          document_type: data.document_type || 'contract'
        });

        // Also set the last known good content
        setLastKnownGoodContent(content);

        // Also set the contentBeforeTabSwitch to ensure tab switching works correctly
        setContentBeforeTabSwitch(content);

        // Save to localStorage as a backup
        try {
          localStorage.setItem('template_content_backup', content);
        } catch (e) {
          console.error('Failed to save content backup:', e);
        }
      }
    } catch (error) {
      console.error('Error loading template:', error);
      toast.error("Error loading template. The template could not be found or you don't have permission to edit it.");
      navigate('/templates');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));

    // Clear validation errors related to this field
    if (validationErrors.length > 0) {
      setValidationErrors(prev => prev.filter(error => !error.toLowerCase().includes(name.toLowerCase())));
    }
  };

  // Removed handleSwitchChange function as is_public field is no longer needed

  const handleContentChange = (content: string) => {
    // Update the form data with the new content
    setFormData(prev => ({ ...prev, content }));

    // Only update lastKnownGoodContent if the content is not empty
    if (content && content.trim() !== '') {
      setLastKnownGoodContent(content);

      // Also update contentBeforeTabSwitch to ensure we have the latest content
      // This prevents content loss when switching tabs
      setContentBeforeTabSwitch(content);

      // Also save to localStorage as a backup
      try {
        localStorage.setItem('template_content_backup', content);
      } catch (e) {
        console.error('Failed to save content backup:', e);
      }
    }

    // Clear content-related validation errors
    if (validationErrors.length > 0) {
      setValidationErrors(prev => prev.filter(error => !error.includes('content')));
    }
  };

  const handleInsertVariable = (variable: Variable) => {
    // Add the variable to the list if it's not already there
    setFormData(prev => {
      if (!prev.variables.some(v => v.id === variable.id)) {
        return {
          ...prev,
          variables: [...prev.variables, variable]
        };
      }
      return prev;
    });
  };

  const validateForm = () => {
    const errors: string[] = [];

    if (!user) {
      errors.push("You must be logged in to save a template.");
    }

    if (!venueId) {
      errors.push("No entity found. Please ensure your account is properly linked to an entity.");
    }

    if (!formData.title.trim()) {
      errors.push("Please enter a title for the template.");
    }

    // Check if content is empty or just contains HTML tags with no actual content
    const contentWithoutTags = formData.content.replace(/<[^>]*>/g, '').trim();
    if (!contentWithoutTags && !formData.content.includes('variable')) {
      errors.push("The template content cannot be empty. Please add some content or variables.");
    }

    return errors;
  };

  const handleSave = async () => {
    // Validate the form
    const errors = validateForm();
    setValidationErrors(errors);

    if (errors.length > 0) {
      // Show the first validation error as a toast
      toast.error(errors[0]);
      // Set focus to the editor if content is empty
      if (errors.some(e => e.includes('content cannot be empty'))) {
        setActiveTab('edit');
      }
      return;
    }

    setSaving(true);
    try {
      // We already have the entity ID in venueId (set by getCurrentVenue)
      if (!venueId) {
        throw new Error('Could not find an entity associated with your user account. Please ensure your account is properly set up.');
      }

      const templateData = {
        title: formData.title,
        description: formData.description,
        content: formData.content,
        variables: JSON.stringify(formData.variables),
        document_type: formData.document_type,
        owner_entity_id: venueId, // Use entity ID as owner_entity_id
        venue_id: venueId // Keep venue_id for backward compatibility
      };

      ('Saving template with data:', {
        ...templateData,
        content: templateData.content.length > 100 ?
          `${templateData.content.substring(0, 100)}... (${templateData.content.length} chars)` :
          templateData.content
      });

      let result: any;

      if (id && id !== 'new') {
        // Update existing template
        result = await supabase
          .from('document_templates')
          .update(templateData)
          .eq('id', id);
      } else {
        // Create new template
        result = await supabase
          .from('document_templates')
          .insert(templateData);
      }

      if (result.error) {
        console.error('Supabase error saving template:', result.error);
        throw result.error;
      }

      ('Template saved successfully:', result.data);

      // Set success state and show success message
      setSaveSuccess(true);
      toast.success(`Template ${id === 'new' ? 'created' : 'updated'} successfully`);

      // Navigate after a short delay to allow the user to see the success message
      setTimeout(() => {
        navigate('/templates');
      }, 1500);
    } catch (error: any) {
      console.error('Error saving template:', error);
      let errorMessage = `Failed to ${id === 'new' ? 'create' : 'update'} template.`;

      // Add more specific error information if available
      if (error.message) {
        if (error.message.includes('foreign key constraint') || error.message.includes('Key is not present in table "entities"')) {
          errorMessage = 'Error: There was a problem with the entity association. Please ensure your account is properly linked to a venue or artist entity.';
        } else if (error.message.includes('duplicate key')) {
          errorMessage = 'Error: A template with this name already exists. Please choose a different name.';
        } else if (error.message.includes('permission denied')) {
          errorMessage = 'Error: You do not have permission to save this template. Please check your account permissions.';
        } else if (error.message.includes('Could not find an entity associated with your user account')) {
          errorMessage = 'Error: Could not find an entity associated with your user account. Please ensure your account is properly set up.';
        } else {
          errorMessage += ` Error: ${error.message}`;
        }
      } else if (error.details) {
        if (error.details.includes('Key is not present in table "entities"')) {
          errorMessage = 'Error: There was a problem with the entity association. Please ensure your account is properly linked to a venue or artist entity.';
        } else {
          errorMessage += ` Details: ${error.details}`;
        }
      } else if (error.code) {
        if (error.code === '23503') { // Foreign key violation
          errorMessage = 'Error: There was a problem with the entity association. Please ensure your account is properly linked to a venue or artist entity.';
        } else {
          errorMessage += ` Code: ${error.code}`;
        }
      }

      toast.error(errorMessage);
    } finally {
      setSaving(false);
    }
  };

  // Memoize the preview to prevent unnecessary re-renders
  const renderPreview = useMemo(() => {
    // Use formData.content, but fall back to lastKnownGoodContent if it's empty
    const contentToRender = formData.content && formData.content.trim() !== ''
      ? formData.content
      : lastKnownGoodContent;

    ('Rendering preview with content length:', contentToRender.length);

    try {
      // If we're using the fallback content, show a warning
      if (contentToRender !== formData.content) {
        console.warn('Using last known good content for preview');
      }

      const renderedContent = replaceVariablesInTemplate(contentToRender, previewData);

      return (
        <div className="space-y-4">
          {contentToRender !== formData.content && (
            <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-3 mb-2">
              <div className="flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2" />
                <p className="text-sm">Using backed-up content for preview. Your current edits will be preserved.</p>
              </div>
            </div>
          )}
          <div className="contract-preview bg-white p-8 border rounded-md shadow-sm overflow-y-auto h-[750px]">
            <div
              className="prose prose-sm md:prose-base lg:prose-lg max-w-none"
              dangerouslySetInnerHTML={{ __html: renderedContent }}
            />
          </div>
        </div>
      );
    } catch (error) {
      console.error('Error rendering preview:', error);

      // Try with the last known good content as a fallback
      if (contentToRender !== lastKnownGoodContent) {
        try {
          ('Attempting to render with last known good content');
          const fallbackContent = replaceVariablesInTemplate(lastKnownGoodContent, previewData);

          return (
            <div className="space-y-4">
              <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-md p-3 mb-2">
                <div className="flex items-center">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  <p className="text-sm">There was an error with your current content. Showing the last valid version.</p>
                </div>
              </div>
              <div className="contract-preview bg-white p-8 border rounded-md shadow-sm overflow-y-auto h-[750px]">
                <div
                  className="prose prose-sm md:prose-base lg:prose-lg max-w-none"
                  dangerouslySetInnerHTML={{ __html: fallbackContent }}
                />
              </div>
            </div>
          );
        } catch (fallbackError) {
          console.error('Error rendering fallback preview:', fallbackError);
        }
      }

      return (
        <div className="bg-red-50 p-8 border border-red-200 rounded-md h-[750px] overflow-y-auto">
          <h3 className="text-red-800 font-medium">Error rendering preview</h3>
          <p className="text-red-700 mt-2">There was an error rendering the preview. Please check your template for any invalid HTML.</p>
          <pre className="mt-4 p-4 bg-white border border-red-100 rounded text-sm overflow-x-auto">
            {String(error)}
          </pre>
        </div>
      );
    }
  }, [formData.content, lastKnownGoodContent, previewData]);

  // Determine user type for layout
  const { isVenue, isAgency } = useAuth();
  const layoutUserType = isVenue ? 'venue' : isAgency ? 'agency' : 'venue';

  return (
    <DashboardLayout userType={layoutUserType}>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)}>
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <div>
              <h1 className="text-2xl font-bold tracking-tight">
                {id === 'new' ? 'Create New Template' : 'Edit Template'}
              </h1>
              <p className="text-sm text-muted-foreground">
                {id === 'new'
                  ? 'Create a new document template with variables'
                  : 'Edit your existing document template'}
              </p>
            </div>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => navigate('/templates')}
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving || loading}
            >
              {saving ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  Save Template
                </>
              )}
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-gray-500" />
          </div>
        ) : (
          <div className="grid gap-6">
            {validationErrors.length > 0 && (
              <div className="bg-red-50 border border-red-200 text-red-800 rounded-md p-4 mb-6">
                <h3 className="text-sm font-medium mb-2">Please fix the following errors:</h3>
                <ul className="list-disc pl-5 space-y-1 text-sm">
                  {validationErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}

            {saveSuccess && (
              <div className="bg-green-50 border border-green-200 text-green-800 rounded-md p-4 mb-6 flex items-center">
                <svg className="h-5 w-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                <span>Template saved successfully! Redirecting to templates list...</span>
              </div>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Template Details</CardTitle>
                <CardDescription>
                  Basic information about your document template
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid gap-4">
                  <div>
                    <Label htmlFor="title">Template Title <span className="text-red-500">*</span></Label>
                    <Input
                      id="title"
                      name="title"
                      value={formData.title}
                      onChange={handleInputChange}
                      placeholder="Enter template title"
                      className={validationErrors.some(e => e.includes('title')) ? 'border-red-500 focus:ring-red-500' : ''}
                      disabled={saving || saveSuccess}
                    />
                    {validationErrors.some(e => e.includes('title')) && (
                      <p className="text-red-500 text-xs mt-1">Please enter a title for the template</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="document_type">Document Type <span className="text-red-500">*</span></Label>
                    <Select
                      value={formData.document_type}
                      onValueChange={(value) => setFormData({ ...formData, document_type: value })}
                      disabled={saving || saveSuccess}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select document type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="contract">Contract</SelectItem>
                        <SelectItem value="rider">Technical Rider</SelectItem>
                        <SelectItem value="callsheet">Call Sheet</SelectItem>
                        <SelectItem value="invoice">Invoice</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Signature option removed from template editor */}

                  <div>
                    <Label htmlFor="description">Description (Optional)</Label>
                    <Textarea
                      id="description"
                      name="description"
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Brief description of this template"
                      rows={3}
                      disabled={saving || saveSuccess}
                    />
                  </div>

                  {/* Public/private switch removed as templates are now only visible to venue users */}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Template Content</CardTitle>
                <CardDescription>
                  Design your document template with variables
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs value={activeTab} onValueChange={handleTabChange}>
                  <TabsList className="mb-4">
                    <TabsTrigger value="edit">
                      <FileText className="h-4 w-4 mr-2" />
                      Edit
                    </TabsTrigger>
                    <TabsTrigger value="preview">
                      <Eye className="h-4 w-4 mr-2" />
                      Preview
                    </TabsTrigger>
                  </TabsList>

                  <TabsContent value="edit" className="space-y-4" forceMount>
                    <div style={{ display: activeTab === 'edit' ? 'block' : 'none' }}>
                      <div className="flex justify-between items-center mb-2">
                        <Label>Template Content <span className="text-red-500">*</span></Label>
                        {validationErrors.some(e => e.includes('content cannot be empty')) && (
                          <p className="text-red-500 text-xs">Template content cannot be empty</p>
                        )}
                      </div>
                      <EnhancedRichTextEditor
                        key="editor-instance" // Add a stable key to prevent re-mounting
                        content={formData.content}
                        onChange={handleContentChange}
                        variables={contractVariables}
                        onInsertVariable={handleInsertVariable}
                        placeholder="Start designing your contract template... Use the toolbar above to format text and add variables."
                        className={`min-h-[750px] ${validationErrors.some(e => e.includes('content cannot be empty')) ? 'border-2 border-red-500' : 'border-2'}`}
                        editorClassName="text-base"
                        readOnly={saving || saveSuccess || activeTab !== 'edit'}
                      />
                    </div>

                    <div className="text-sm text-muted-foreground" style={{ display: activeTab === 'edit' ? 'block' : 'none' }}>
                      <p>Use the variable button in the toolbar to insert dynamic content that will be replaced with actual data when the document is generated.</p>
                    </div>
                  </TabsContent>

                  <TabsContent value="preview" className="min-h-[750px]" forceMount>
                    <div style={{ display: activeTab === 'preview' ? 'block' : 'none' }}>
                      {renderPreview}
                      <div className="text-sm text-muted-foreground mt-4">
                        <p>This preview shows how the document will look with sample data. Actual documents will use real booking data.</p>
                      </div>
                    </div>
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  onClick={() => navigate('/templates')}
                  disabled={saving}
                >
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={saving || saveSuccess}
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Save Template
                    </>
                  )}
                </Button>
              </CardFooter>
            </Card>
          </div>
        )}
      </div>
    </DashboardLayout>
  );
};

export default TemplateEditor;
