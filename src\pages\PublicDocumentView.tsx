import { useState, useEffect, useRef } from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Download, File, Calendar, FileText, ExternalLink, Copy, Check } from 'lucide-react';
import { getDocumentByShareId } from '@/features/documents/api/documentHelpers';
import { format } from 'date-fns';
import html2pdf from 'html2pdf.js';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

const PublicDocumentView = () => {
  const { shareId } = useParams<{ shareId: string }>();
  const [document, setDocument] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (shareId) {
      loadDocument(shareId);
    }
  }, [shareId]);

  const loadDocument = async (docShareId: string) => {
    setLoading(true);
    try {
      const documentData = await getDocumentByShareId(docShareId);

      if (!documentData) {
        setError("The requested document could not be found or is not publicly shared.");
        return;
      }

      setDocument(documentData);
    } catch (err) {
      console.error("Error loading document:", err);
      setError("There was a problem retrieving the document.");
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = () => {
    // Rename our document variable to avoid conflict with the global document object
    const docData = document;

    if (docData?.file_url) {
      // For uploaded files, just open the URL
      window.open(docData.file_url, '_blank');
    } else if (docData?.content) {
      // For generated documents, create a PDF
      try {
        // Create a temporary div to hold the document content
        const element = window.document.createElement('div');
        element.className = 'document-for-pdf';
        element.style.padding = '20px';
        element.style.fontFamily = 'Arial, sans-serif';
        element.style.maxWidth = '800px';
        element.style.margin = '0 auto';

        // Add document content with proper styling
        const contentDiv = window.document.createElement('div');
        contentDiv.className = 'document-content';

        // Apply styles to ensure rich text formatting is preserved
        contentDiv.style.fontSize = '12pt';
        contentDiv.style.lineHeight = '1.5';
        contentDiv.style.color = '#000';

        // Add document content directly (no title)
        if (typeof docData.content === 'string') {
          contentDiv.innerHTML = docData.content;

          // Apply styles to common HTML elements to ensure proper formatting
          const styleElement = window.document.createElement('style');
          styleElement.textContent = `
            .document-for-pdf h1 { font-size: 18pt; margin-bottom: 12pt; font-weight: bold; }
            .document-for-pdf h2 { font-size: 16pt; margin-bottom: 10pt; font-weight: bold; }
            .document-for-pdf h3 { font-size: 14pt; margin-bottom: 8pt; font-weight: bold; }
            .document-for-pdf p { margin-bottom: 10pt; }
            .document-for-pdf ul, .document-for-pdf ol { margin-left: 20pt; margin-bottom: 10pt; }
            .document-for-pdf li { margin-bottom: 5pt; }
            .document-for-pdf table { width: 100%; border-collapse: collapse; margin-bottom: 15pt; }
            .document-for-pdf th, .document-for-pdf td { border: 1px solid #ddd; padding: 8pt; text-align: left; }
            .document-for-pdf th { background-color: #f2f2f2; font-weight: bold; }
            .document-for-pdf a { color: #0066cc; text-decoration: underline; }
            .document-for-pdf blockquote { margin-left: 20pt; padding-left: 10pt; border-left: 3pt solid #ddd; font-style: italic; }
          `;
          element.appendChild(styleElement);
        } else if (typeof docData.content === 'object') {
          contentDiv.innerHTML = docData.content.terms || docData.content.details || docData.content.requirements || '';
        }
        element.appendChild(contentDiv);

        // Add signature information if document is signed
        if (docData.signed_by_artist || docData.signed_by_venue) {
          const signaturesDiv = window.document.createElement('div');
          signaturesDiv.style.marginTop = '30px';
          signaturesDiv.style.borderTop = '1px solid #ccc';
          signaturesDiv.style.paddingTop = '20px';

          const signaturesTitle = window.document.createElement('h3');
          signaturesTitle.textContent = 'Signatures';
          signaturesTitle.style.fontSize = '16px';
          signaturesTitle.style.marginBottom = '10px';
          signaturesDiv.appendChild(signaturesTitle);

          if (docData.signed_by_artist) {
            const artistSig = window.document.createElement('p');
            artistSig.textContent = `Artist signature: Signed${docData.artist_signature_date ? ` on ${format(new Date(docData.artist_signature_date), 'PPP')}` : ''}`;
            signaturesDiv.appendChild(artistSig);
          }

          if (docData.signed_by_venue) {
            const venueSig = window.document.createElement('p');
            venueSig.textContent = `Venue signature: Signed${docData.venue_signature_date ? ` on ${format(new Date(docData.venue_signature_date), 'PPP')}` : ''}`;
            signaturesDiv.appendChild(venueSig);
          }

          element.appendChild(signaturesDiv);
        }

        // Add to document body temporarily
        window.document.body.appendChild(element);

        // Generate PDF with better options for rich text
        const opt = {
          margin: [15, 15, 15, 15], // top, right, bottom, left margins in mm
          filename: `${docData.title.replace(/\s+/g, '_')}.pdf`,
          image: { type: 'jpeg', quality: 1 },
          html2canvas: {
            scale: 2, // Higher scale for better quality
            useCORS: true, // Enable CORS for images
            logging: false,
            letterRendering: true,
            allowTaint: true
          },
          jsPDF: {
            unit: 'mm',
            format: 'a4',
            orientation: 'portrait',
            compress: true
          }
        };

        html2pdf().set(opt).from(element).save();

        // Remove the temporary element
        window.document.body.removeChild(element);
      } catch (error) {
        console.error('Error generating PDF:', error);
      }
    }
  };

  const handleCopyLink = () => {
    const currentUrl = window.location.href;
    navigator.clipboard.writeText(currentUrl);
    setCopied(true);
    toast.success("Link copied to clipboard");

    // Reset copied state after 2 seconds
    setTimeout(() => {
      setCopied(false);
    }, 2000);
  };

  const renderDocumentContent = () => {
    if (!document) {
      return null;
    }

    if (document.content) {
      if (typeof document.content === 'string') {
        return (
          <div className="prose prose-sm max-w-none" dangerouslySetInnerHTML={{ __html: document.content }} />
        );
      } else if (typeof document.content === 'object') {
        return (
          <div className="prose prose-sm max-w-none">
            <p>{document.content.terms || document.content.details || document.content.requirements || "No content specified."}</p>
          </div>
        );
      }
    } else if (document.file_url) {
      const isPdf = document.file_url.toLowerCase().endsWith('.pdf');

      if (isPdf) {
        return (
          <div className="h-[600px] border rounded-md overflow-hidden">
            <iframe
              src={`${document.file_url}#toolbar=0`}
              className="w-full h-full"
              title={document.title}
            />
          </div>
        );
      } else {
        return (
          <div className="text-center py-12 border rounded-md">
            <File className="h-12 w-12 mx-auto text-gray-400 mb-2" />
            <h3 className="text-lg font-medium mb-2">Document Preview Not Available</h3>
            <p className="text-gray-500 mb-4">This file type cannot be previewed directly in the browser.</p>
            <Button onClick={handleDownload}>
              <Download className="mr-2 h-4 w-4" />
              Download to View
            </Button>
          </div>
        );
      }
    }

    return (
      <div className="text-center py-12 border rounded-md">
        <File className="h-12 w-12 mx-auto text-gray-400 mb-2" />
        <h3 className="text-lg font-medium mb-2">No Content Available</h3>
        <p className="text-gray-500">This document does not have any content to display.</p>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-5xl">
        <div className="text-center py-12">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mb-4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto mb-8"></div>
            <div className="h-64 bg-gray-200 rounded w-full mx-auto"></div>
          </div>
          <p className="mt-4 text-gray-500">Loading document...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto px-4 py-8 max-w-5xl">
        <div className="text-center py-12 border rounded-md">
          <File className="h-12 w-12 mx-auto text-gray-400 mb-2" />
          <h3 className="text-lg font-medium mb-2">Document Not Available</h3>
          <p className="text-gray-500 mb-4">{error}</p>
          <Button asChild variant="outline">
            <Link to="/login">
              <ExternalLink className="mr-2 h-4 w-4" />
              Log in to StageCloud
            </Link>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 max-w-5xl">
      <div className="flex flex-col space-y-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold">{document?.title}</h1>
            <p className="text-gray-500">
              {document?.document_type && (
                <Badge variant="outline" className="mr-2">
                  {document.document_type.charAt(0).toUpperCase() + document.document_type.slice(1)}
                </Badge>
              )}
              Shared document from StageCloud
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleDownload}>
              <Download className="mr-1 h-4 w-4" />
              Download
            </Button>
            <Button variant="outline" onClick={handleCopyLink}>
              {copied ? (
                <Check className="mr-1 h-4 w-4" />
              ) : (
                <Copy className="mr-1 h-4 w-4" />
              )}
              {copied ? "Copied!" : "Copy Link"}
            </Button>
            {document?.is_signable ?
            <Button asChild variant="default">
              <Link to={`/documents/${document?.id}`}>
                <ExternalLink className="mr-1 h-4 w-4" />
                  Log in to Sign
              </Link>
            </Button>
            : ''}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Document Content</CardTitle>
              </CardHeader>
              <CardContent>
                <div ref={contentRef}>
                  {renderDocumentContent()}
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {document?.booking_title && (
              <Card>
                <CardHeader>
                  <CardTitle>Booking Details</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      Event
                    </h3>
                    <p className="text-sm">{document.booking_title}</p>
                  </div>

                  {document.booking_start && (
                    <div>
                      <h3 className="font-medium flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                        Date & Time
                      </h3>
                      <p className="text-sm">
                        {format(new Date(document.booking_start), 'PPP')} at {format(new Date(document.booking_start), 'p')}
                      </p>
                    </div>
                  )}

                  <div className="pt-2">
                    <Button variant="outline" size="sm" asChild className="w-full">
                      <Link to={document.booking_id ? `/bookings/${document.booking_id}` : "/"}>
                        <ExternalLink className="mr-2 h-4 w-4" />
                        View Booking on StageCloud
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            )}

            <Card>
              <CardHeader>
                <CardTitle>Document Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h3 className="font-medium flex items-center">
                    <FileText className="h-4 w-4 mr-2 text-gray-500" />
                    Type
                  </h3>
                  <p className="text-sm capitalize">{document?.document_type || 'Document'}</p>
                </div>

                {document?.created_at && (
                  <div>
                    <h3 className="font-medium flex items-center">
                      <Calendar className="h-4 w-4 mr-2 text-gray-500" />
                      Created
                    </h3>
                    <p className="text-sm">
                      {format(new Date(document.created_at), 'PPP')}
                    </p>
                  </div>
                )}

                {document?.description && (
                  <div>
                    <h3 className="font-medium flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-gray-500" />
                      Description
                    </h3>
                    <p className="text-sm">{document.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        <div className="text-center mt-8 space-y-4">
          <div className="text-sm text-gray-500">
            <p>This document is shared via StageCloud</p>
            <p className="mt-1">
              <Link to="/login" className="text-blue-600 hover:underline">
                Log in to StageCloud
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PublicDocumentView;
